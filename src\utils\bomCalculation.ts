/**
 * BOM计算相关的配置和工具函数
 * 支持动态配置，避免硬编码，提高可维护性
 */

// 默认单位转换配置
const DEFAULT_UNIT_CONVERSION_CONFIG = {
  // 重量单位配置
  WEIGHT_UNITS: {
    baseUnit: 'KG',
    conversions: [
      { keywords: ['kg', '千克', '公斤'], factor: 1 },
      { keywords: ['g', '克'], factor: 0.001 },
      { keywords: ['t', '吨'], factor: 1000 },
      { keywords: ['mg', '毫克'], factor: 0.000001 }
    ]
  },
  // 体积单位配置
  VOLUME_UNITS: {
    baseUnit: 'L',
    conversions: [
      { keywords: ['l', '升', '公升'], factor: 1 },
      { keywords: ['ml', '毫升'], factor: 0.001 },
      { keywords: ['m³', '立方米'], factor: 1000 },
      { keywords: ['cm³', '立方厘米'], factor: 0.001 }
    ]
  },
  // 数量单位配置
  QUANTITY_UNITS: {
    baseUnit: 'PCS',
    conversions: [
      { keywords: ['个', '支', '条', '件', '只', '台', '根', '张', '片', '枚', '颗'], factor: 1 }
    ]
  }
}

// 可配置的单位转换配置
const UNIT_CONVERSION_CONFIG = { ...DEFAULT_UNIT_CONVERSION_CONFIG }

// 默认规格解析配置
const DEFAULT_SPEC_PARSING_CONFIG = {
  // 特殊规格模式
  SPECIAL_PATTERNS: [
    {
      name: 'TON_BARREL',
      regex: /(\d+(?:\.\d+)?)\s*吨/,
      keywords: ['吨桶', '吨/桶'],
      handler: (match: RegExpMatchArray) => ({
        value: match ? parseFloat(match[1]) * 1000 : 1000,
        unit: 'KG',
        packageUnit: '桶'
      })
    },
    {
      name: 'BULK_PACKAGING',
      regex: /(\d+(?:\.\d+)?)\s*(kg|t|g)\s*\/\s*(桶|罐|缸)/i,
      keywords: ['桶', '罐', '缸'],
      handler: (match: RegExpMatchArray) => {
        let value = parseFloat(match[1])
        const unit = match[2].toUpperCase()

        // 统一转换为KG
        if (unit === 'G') value = value / 1000
        if (unit === 'T') value = value * 1000

        return {
          value: value,
          unit: 'KG',
          packageUnit: match[3]
        }
      }
    }
  ],
  // 标准格式模式
  STANDARD_PATTERNS: [
    {
      name: 'COMPOUND_FORMAT',
      regex: /(\d+(?:\.\d+)?)\s*([a-z\u4e00-\u9fa5]+)\s*\*\s*(\d+(?:\.\d+)?)\s*([^\/]+)\/(\S+)/i,
      handler: (match: RegExpMatchArray, packageUnit?: string) => {
        // 解析复合规格，如 "1kg*12瓶/件"
        const unitWeight = parseFloat(match[1])
        const weightUnit = match[2].toUpperCase()
        const unitsPerPackage = parseFloat(match[3])
        const individualUnit = match[4] // 如 "瓶"
        const finalPackageUnit = packageUnit || match[5]

        // 计算每包装的总重量
        let totalWeightPerPackage = unitWeight * unitsPerPackage

        // 统一转换为KG
        if (weightUnit === 'G') {
          totalWeightPerPackage = totalWeightPerPackage / 1000
        } else if (weightUnit === 'T') {
          totalWeightPerPackage = totalWeightPerPackage * 1000
        }

        return {
          value: totalWeightPerPackage,
          unit: 'KG',
          packageUnit: finalPackageUnit,
          // 复合规格信息
          isCompound: true,
          unitWeight: unitWeight,
          unitWeightUnit: weightUnit,
          unitsPerPackage: unitsPerPackage,
          individualUnit: individualUnit
        }
      }
    },
    {
      name: 'VOLUME_FORMAT',
      regex: /(\d+(?:\.\d+)?)\s*(l|ml)\/(\S+)/i,
      handler: (volumeMatch: RegExpMatchArray, packageMatch: string) => {
        let volume = parseFloat(volumeMatch[1])
        const volumeUnit = volumeMatch[2].toUpperCase()
        if (volumeUnit === 'ML') volume = volume / 1000
        return { value: volume, unit: 'L', packageUnit: packageMatch }
      }
    },
    {
      name: 'WEIGHT_FORMAT',
      regex: /(\d+(?:\.\d+)?)\s*([a-z]+)\/(\S+)/i,
      handler: (match: RegExpMatchArray, packageUnit?: string) => ({
        value: parseFloat(match[1]),
        unit: match[2].toUpperCase(),
        packageUnit: packageUnit || match[3]
      })
    }
  ],
  // 默认包装单位
  DEFAULT_PACKAGE_UNITS: [
    { keywords: ['桶', '罐', '缸'], unit: '桶' },
    { keywords: ['袋', '包'], unit: '袋' },
    { keywords: ['瓶', '罐'], unit: '瓶' },
    { keywords: ['盒', '箱'], unit: '盒' },
    { keywords: ['卷', '捆'], unit: '卷' },
    { keywords: ['板', '块'], unit: '块' }
  ]
}

// 可配置的规格解析配置
const SPEC_PARSING_CONFIG = { ...DEFAULT_SPEC_PARSING_CONFIG }

// 默认计算精度配置
const DEFAULT_CALCULATION_CONFIG = {
  // 数量精度（小数位数）
  QUANTITY_PRECISION: 4,
  // 金额精度（小数位数）
  AMOUNT_PRECISION: 2,
  // 规格数量精度（小数位数）
  SPEC_QUANTITY_PRECISION: 0, // 包装数量通常为整数
  // 向上取整的阈值
  CEILING_THRESHOLD: 0.001
}

// 默认业务规则配置
const DEFAULT_BUSINESS_RULES_CONFIG = {
  // 默认每槽数量
  DEFAULT_SLOT_QUANTITY: 1,
  // 最小订单数量
  MIN_ORDER_QUANTITY: 0.001,
  // 最大槽数限制
  MAX_SLOT_COUNT: 1000,
  // 是否启用智能规格计算
  ENABLE_SMART_SPEC_CALCULATION: true,
  // 是否启用单位自动转换
  ENABLE_AUTO_UNIT_CONVERSION: true
}

// 可配置的计算精度配置
const CALCULATION_CONFIG = { ...DEFAULT_CALCULATION_CONFIG }

// 可配置的业务规则配置
const BUSINESS_RULES_CONFIG = { ...DEFAULT_BUSINESS_RULES_CONFIG }

// 导出配置访问器，保持向后兼容
export {
  UNIT_CONVERSION_CONFIG,
  SPEC_PARSING_CONFIG,
  CALCULATION_CONFIG,
  BUSINESS_RULES_CONFIG
}

// 配置管理函数
export const ConfigManager = {
  /**
   * 更新单位转换配置
   * @param config 新的单位转换配置
   */
  updateUnitConversionConfig(config: Partial<typeof DEFAULT_UNIT_CONVERSION_CONFIG>) {
    Object.assign(UNIT_CONVERSION_CONFIG, config)
  },

  /**
   * 更新规格解析配置
   * @param config 新的规格解析配置
   */
  updateSpecParsingConfig(config: Partial<typeof DEFAULT_SPEC_PARSING_CONFIG>) {
    Object.assign(SPEC_PARSING_CONFIG, config)
  },

  /**
   * 更新计算精度配置
   * @param config 新的计算精度配置
   */
  updateCalculationConfig(config: Partial<typeof DEFAULT_CALCULATION_CONFIG>) {
    Object.assign(CALCULATION_CONFIG, config)
  },

  /**
   * 更新业务规则配置
   * @param config 新的业务规则配置
   */
  updateBusinessRulesConfig(config: Partial<typeof DEFAULT_BUSINESS_RULES_CONFIG>) {
    Object.assign(BUSINESS_RULES_CONFIG, config)
  },

  /**
   * 重置所有配置为默认值
   */
  resetToDefaults() {
    Object.assign(UNIT_CONVERSION_CONFIG, DEFAULT_UNIT_CONVERSION_CONFIG)
    Object.assign(SPEC_PARSING_CONFIG, DEFAULT_SPEC_PARSING_CONFIG)
    Object.assign(CALCULATION_CONFIG, DEFAULT_CALCULATION_CONFIG)
    Object.assign(BUSINESS_RULES_CONFIG, DEFAULT_BUSINESS_RULES_CONFIG)
  },

  /**
   * 获取当前配置
   */
  getCurrentConfig() {
    return {
      unitConversion: { ...UNIT_CONVERSION_CONFIG },
      specParsing: { ...SPEC_PARSING_CONFIG },
      calculation: { ...CALCULATION_CONFIG },
      businessRules: { ...BUSINESS_RULES_CONFIG }
    }
  }
}

// 行数据类型定义
export interface RowData {
  materialName?: string
  spec?: string
  unit?: number
  slotQuantity?: number
  slotSpecQuantity?: number | string  // 支持字符串格式，如 "6袋+19千克"
  plannedQuantity?: number
  plannedSpecQuantity?: number | string  // 支持字符串格式
  [key: string]: any
}

// 单位信息类型定义
export interface UnitInfo {
  factor: number
  baseUnit: string
}

// 规格信息类型定义
export interface SpecInfo {
  value: number
  unit: string
  packageUnit: string
  // 复合规格信息
  isCompound?: boolean
  unitWeight?: number
  unitWeightUnit?: string
  unitsPerPackage?: number
  individualUnit?: string
}

// 规格计算结果类型定义
export interface SpecCalculationResult {
  packages: number          // 完整包装数量
  remainder: number         // 余量
  remainderUnit: string     // 余量单位
  displayText: string       // 显示文本，如 "6袋+19千克"
  totalPackages: number     // 向上取整的总包装数
}

/**
 * 获取单位转换信息
 * @param unitName 单位名称
 * @returns 单位转换信息
 */
export const getUnitConversionFactor = (unitName: string): UnitInfo => {
  const unitNameLower = unitName.toLowerCase()
  
  // 检查重量单位
  for (const conversion of UNIT_CONVERSION_CONFIG.WEIGHT_UNITS.conversions) {
    if (conversion.keywords.some(keyword => unitNameLower.includes(keyword))) {
      return { factor: conversion.factor, baseUnit: UNIT_CONVERSION_CONFIG.WEIGHT_UNITS.baseUnit }
    }
  }
  
  // 检查体积单位
  for (const conversion of UNIT_CONVERSION_CONFIG.VOLUME_UNITS.conversions) {
    if (conversion.keywords.some(keyword => unitNameLower.includes(keyword))) {
      return { factor: conversion.factor, baseUnit: UNIT_CONVERSION_CONFIG.VOLUME_UNITS.baseUnit }
    }
  }
  
  // 检查数量单位
  for (const conversion of UNIT_CONVERSION_CONFIG.QUANTITY_UNITS.conversions) {
    if (conversion.keywords.some(keyword => unitNameLower.includes(keyword))) {
      return { factor: conversion.factor, baseUnit: UNIT_CONVERSION_CONFIG.QUANTITY_UNITS.baseUnit }
    }
  }
  
  // 默认按数量处理
  return { factor: 1, baseUnit: UNIT_CONVERSION_CONFIG.QUANTITY_UNITS.baseUnit }
}

/**
 * 解析规格信息
 * @param spec 规格字符串
 * @returns 规格信息
 */
export const parseSpecification = (spec: string): SpecInfo => {
  if (!spec) return { value: 1, unit: '', packageUnit: '个' }

  const specLower = spec.toLowerCase().trim()
  
  // 处理特殊规格模式
  for (const pattern of SPEC_PARSING_CONFIG.SPECIAL_PATTERNS) {
    const hasKeywords = pattern.keywords.some(keyword => specLower.includes(keyword))
    if (hasKeywords) {
      const match = spec.match(pattern.regex)
      if (match) {
        return pattern.handler(match)
      }
    }
  }
  
  // 处理标准格式模式
  for (const pattern of SPEC_PARSING_CONFIG.STANDARD_PATTERNS) {
    if (pattern.name === 'COMPOUND_FORMAT') {
      // 检查是否包含复合格式的特征：数字*数字/单位
      if (specLower.includes('*') && specLower.includes('/')) {
        const match = spec.match(pattern.regex)
        if (match) {
          return pattern.handler(match, '')
        }
      }
    } else if (pattern.name === 'VOLUME_FORMAT') {
      if (specLower.includes('l/') || specLower.includes('ml/')) {
        const volumeMatch = spec.match(/(\d+(?:\.\d+)?)\s*(l|ml)/i)
        const packageMatch = spec.match(/\/(\S+)$/)
        if (volumeMatch && packageMatch) {
          return pattern.handler(volumeMatch, packageMatch[1])
        }
      }
    } else if (pattern.name === 'WEIGHT_FORMAT') {
      const match = spec.match(pattern.regex)
      if (match) {
        return pattern.handler(match, '')
      }
    }
  }
  
  // 只有数字和单位，没有包装信息
  const unitMatch = spec.match(/(\d+(?:\.\d+)?)\s*([a-z]+)/i)
  if (unitMatch) {
    return {
      value: parseFloat(unitMatch[1]),
      unit: unitMatch[2].toUpperCase(),
      packageUnit: '个'
    }
  }
  
  // 只有数字，没有单位
  const numberMatch = spec.match(/(\d+(?:\.\d+)?)/)
  if (numberMatch) {
    return {
      value: parseFloat(numberMatch[1]),
      unit: '',
      packageUnit: '个'
    }
  }
  
  // 只有文字描述，没有数字
  for (const defaultUnit of SPEC_PARSING_CONFIG.DEFAULT_PACKAGE_UNITS) {
    if (defaultUnit.keywords.some(keyword => specLower.includes(keyword))) {
      return { value: 1, unit: '', packageUnit: defaultUnit.unit }
    }
  }
  
  // 默认情况
  return { value: 1, unit: '', packageUnit: '个' }
}

/**
 * 格式化数值到指定精度
 * @param value 数值
 * @param precision 精度
 * @returns 格式化后的数值
 */
export const formatToPrecision = (value: number, precision: number): number => {
  return parseFloat(value.toFixed(precision))
}

/**
 * 计算总槽数
 * @param orderQuantity 订单数量
 * @param slotQuantity 每槽数量
 * @returns 总槽数
 */
export const calculateTotalSlots = (orderQuantity: number, slotQuantity: number): number => {
  if (!orderQuantity || !slotQuantity || slotQuantity <= 0) {
    return 0
  }

  const totalSlots = Math.ceil(orderQuantity / slotQuantity)

  // 检查是否超过最大槽数限制
  if (totalSlots > BUSINESS_RULES_CONFIG.MAX_SLOT_COUNT) {
    return BUSINESS_RULES_CONFIG.MAX_SLOT_COUNT
  }

  return totalSlots
}

/**
 * 计算规格用量（支持余数显示）
 * @param slotQuantity 每槽用量
 * @param specInfo 规格信息
 * @param materialUnitInfo 物料单位信息
 * @param materialUnitName 物料原始单位名称
 * @returns 规格计算结果
 */
export const calculateSpecQuantityWithRemainder = (
  slotQuantity: number,
  specInfo: SpecInfo,
  materialUnitInfo: UnitInfo,
  materialUnitName: string
): SpecCalculationResult => {
  // 确保单位名称不为空
  const safeUnitName = materialUnitName || '个'

  // 将物料用量转换为基础单位
  const baseQuantity = slotQuantity * materialUnitInfo.factor

  // 如果没有规格信息或规格值为0，返回默认结果
  if (!specInfo.value || specInfo.value <= 0) {
    return {
      packages: 0,
      remainder: slotQuantity,
      remainderUnit: safeUnitName,
      displayText: `${formatToPrecision(slotQuantity, 2)}${safeUnitName}`,
      totalPackages: Math.ceil(slotQuantity)
    }
  }

  // 根据单位类型进行计算
  if (materialUnitInfo.baseUnit === 'KG' && ['KG', 'G', 'T'].includes(specInfo.unit)) {
    // 重量单位匹配
    let packageWeightInKg = specInfo.value

    // 规格单位转换为KG
    if (specInfo.unit === 'G') {
      packageWeightInKg = specInfo.value / 1000
    } else if (specInfo.unit === 'T') {
      packageWeightInKg = specInfo.value * 1000
    }

    // 计算完整包装数和余量
    const packages = Math.floor(baseQuantity / packageWeightInKg)
    const remainderInBaseUnit = baseQuantity - (packages * packageWeightInKg)
    const totalPackages = Math.ceil(baseQuantity / packageWeightInKg)

    // 将余量转换回物料原始单位
    const remainderInOriginalUnit = remainderInBaseUnit / materialUnitInfo.factor

    // 生成显示文本
    let displayText = ''
    if (packages > 0 && remainderInBaseUnit > 0) {
      // 有完整包装和余量
      if (specInfo.isCompound && specInfo.unitWeight && specInfo.individualUnit) {
        // 复合规格：将余量转换为个体单位，如 "83件+4瓶"
        let remainderInIndividualUnits = remainderInBaseUnit

        // 根据单位重量的单位进行转换
        if (specInfo.unitWeightUnit === 'G') {
          remainderInIndividualUnits = remainderInBaseUnit * 1000 // kg转g
        } else if (specInfo.unitWeightUnit === 'T') {
          remainderInIndividualUnits = remainderInBaseUnit / 1000 // kg转t
        }
        // 如果是KG，不需要转换

        // 计算余量对应的个体数量
        const remainderIndividualCount = Math.floor(remainderInIndividualUnits / specInfo.unitWeight)

        if (remainderIndividualCount > 0) {
          displayText = `${packages}${specInfo.packageUnit}+${remainderIndividualCount}${specInfo.individualUnit}`
        } else {
          displayText = `${packages}${specInfo.packageUnit}`
        }
      } else {
        // 普通规格：如 "6袋+19千克"
        displayText = `${packages}${specInfo.packageUnit}+${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
      }
    } else if (packages > 0) {
      // 只有完整包装（能够整除）：如 "6袋(300千克)"
      const totalOriginalQuantity = packages * (packageWeightInKg / materialUnitInfo.factor)
      displayText = `${packages}${specInfo.packageUnit}(${formatToPrecision(totalOriginalQuantity, 2)}${safeUnitName})`
    } else {
      // 只有余量：如 "19千克"
      displayText = `${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
    }

    // 计算余数信息
    let remainderValue = formatToPrecision(remainderInOriginalUnit, 2)
    let remainderUnit = safeUnitName

    if (specInfo.isCompound && specInfo.unitWeight && specInfo.individualUnit && remainderInBaseUnit > 0) {
      // 复合规格的余数以个体单位显示
      let remainderInIndividualUnits = remainderInBaseUnit

      // 根据单位重量的单位进行转换
      if (specInfo.unitWeightUnit === 'G') {
        remainderInIndividualUnits = remainderInBaseUnit * 1000 // kg转g
      } else if (specInfo.unitWeightUnit === 'T') {
        remainderInIndividualUnits = remainderInBaseUnit / 1000 // kg转t
      }

      const remainderIndividualCount = Math.floor(remainderInIndividualUnits / specInfo.unitWeight)
      if (remainderIndividualCount > 0) {
        remainderValue = remainderIndividualCount
        remainderUnit = specInfo.individualUnit
      }
    }

    return {
      packages,
      remainder: remainderValue,
      remainderUnit: remainderUnit,
      displayText,
      totalPackages
    }

  } else if (materialUnitInfo.baseUnit === 'L' && ['L', 'ML'].includes(specInfo.unit)) {
    // 体积单位匹配
    let packageVolumeInL = specInfo.value

    if (specInfo.unit === 'ML') {
      packageVolumeInL = specInfo.value / 1000
    }

    const packages = Math.floor(baseQuantity / packageVolumeInL)
    const remainderInBaseUnit = baseQuantity - (packages * packageVolumeInL)
    const totalPackages = Math.ceil(baseQuantity / packageVolumeInL)

    // 将余量转换回物料原始单位
    const remainderInOriginalUnit = remainderInBaseUnit / materialUnitInfo.factor

    let displayText = ''
    if (packages > 0 && remainderInOriginalUnit > 0) {
      displayText = `${packages}${specInfo.packageUnit}+${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
    } else if (packages > 0) {
      // 只有完整包装（能够整除）：如 "2个(20升)"
      const totalOriginalQuantity = packages * (packageVolumeInL / materialUnitInfo.factor)
      displayText = `${packages}${specInfo.packageUnit}(${formatToPrecision(totalOriginalQuantity, 2)}${safeUnitName})`
    } else {
      displayText = `${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
    }

    return {
      packages,
      remainder: formatToPrecision(remainderInOriginalUnit, 2),
      remainderUnit: safeUnitName,
      displayText,
      totalPackages
    }

  } else {
    // 单位不匹配或数量单位，使用简单计算
    const packages = Math.floor(slotQuantity / specInfo.value)
    const remainderInOriginalUnit = slotQuantity - (packages * specInfo.value)
    const totalPackages = Math.ceil(slotQuantity / specInfo.value)

    let displayText = ''
    if (packages > 0 && remainderInOriginalUnit > 0) {
      displayText = `${packages}${specInfo.packageUnit}+${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
    } else if (packages > 0) {
      // 只有完整包装（能够整除）：如 "5包(500个)"
      const totalOriginalQuantity = packages * specInfo.value
      displayText = `${packages}${specInfo.packageUnit}(${formatToPrecision(totalOriginalQuantity, 2)}${safeUnitName})`
    } else {
      displayText = `${formatToPrecision(remainderInOriginalUnit, 2)}${safeUnitName}`
    }

    return {
      packages,
      remainder: formatToPrecision(remainderInOriginalUnit, 2),
      remainderUnit: safeUnitName,
      displayText,
      totalPackages
    }
  }
}

// 默认单位显示名称映射
const DEFAULT_UNIT_DISPLAY_MAP: Record<string, string> = {
  'KG': '千克',
  'G': '克',
  'T': '吨',
  'L': '升',
  'ML': '毫升',
  'PCS': '个'
}

// 可配置的单位显示名称映射
let UNIT_DISPLAY_MAP = { ...DEFAULT_UNIT_DISPLAY_MAP }

/**
 * 获取单位的中文显示名称
 * @param unit 单位英文名称
 * @returns 中文显示名称
 */
export const getUnitDisplayName = (unit: string): string => {
  return UNIT_DISPLAY_MAP[unit] || unit
}

/**
 * 更新单位显示名称映射
 * @param unitMap 新的单位显示名称映射
 */
export const updateUnitDisplayMap = (unitMap: Record<string, string>) => {
  Object.assign(UNIT_DISPLAY_MAP, unitMap)
}

/**
 * 重置单位显示名称映射为默认值
 */
export const resetUnitDisplayMap = () => {
  UNIT_DISPLAY_MAP = { ...DEFAULT_UNIT_DISPLAY_MAP }
}
